{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Chat.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Send, Save, X } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Chat = () => {\n  _s();\n  const {\n    user: authUser\n  } = useAuth();\n  const {\n    darkMode\n  } = useTheme();\n  const [messages, setMessages] = useState([{\n    id: 1,\n    text: \"Good morning, beautiful! ☀️\",\n    sender: 'other',\n    timestamp: new Date(Date.now() - 3600000),\n    reactions: ['❤️']\n  }, {\n    id: 2,\n    text: \"Good morning, love! Hope you slept well 💕\",\n    sender: 'me',\n    timestamp: new Date(Date.now() - 3500000),\n    reactions: []\n  }]);\n  const [newMessage, setNewMessage] = useState('');\n  const [editingMessage, setEditingMessage] = useState(null);\n  const [editContent, setEditContent] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [otherUserTyping, setOtherUserTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  // Use authUser instead of user\n  const user = authUser;\n\n  // Mock other user data\n  const otherUser = {\n    name: \"My Love 💕\",\n    avatar: \"💖\"\n  };\n  const handleSendMessage = e => {\n    e.preventDefault();\n    if (newMessage.trim()) {\n      const message = {\n        id: Date.now(),\n        text: newMessage,\n        sender: 'me',\n        timestamp: new Date(),\n        reactions: []\n      };\n      setMessages([...messages, message]);\n      setNewMessage('');\n    }\n  };\n  const handleSaveEdit = () => {\n    setMessages(messages.map(msg => msg.id === editingMessage ? {\n      ...msg,\n      text: editContent\n    } : msg));\n    setEditingMessage(null);\n    setEditContent('');\n  };\n  const handleCancelEdit = () => {\n    setEditingMessage(null);\n    setEditContent('');\n  };\n  const handleEditKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSaveEdit();\n    } else if (e.key === 'Escape') {\n      handleCancelEdit();\n    }\n  };\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages]);\n  const formatTime = date => {\n    return new Date(date).toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 text-lg font-medium\",\n          children: \"Loading your conversation...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen transition-colors duration-300 ${darkMode ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' : 'bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto p-4 h-screen flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${darkMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-pink-200'} backdrop-blur-sm rounded-t-2xl p-4 border-b shadow-lg`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center text-2xl animate-pulse\",\n              children: \"\\uD83D\\uDC95\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: `text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n                children: otherUser.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`,\n                children: otherUserTyping ? 'Typing...' : 'Online'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex-1 ${darkMode ? 'bg-gray-800/50' : 'bg-white/50'} backdrop-blur-sm overflow-y-auto p-4 space-y-4 custom-scrollbar`,\n        children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-lg ${message.sender === 'me' ? darkMode ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white' : 'bg-gradient-to-r from-pink-500 to-rose-500 text-white' : darkMode ? 'bg-gray-700 text-gray-100 border border-gray-600' : 'bg-white text-gray-800 border border-gray-200'} relative group`,\n            children: editingMessage === message.id ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: editContent,\n                onChange: e => setEditContent(e.target.value),\n                onKeyPress: handleEditKeyPress,\n                className: `w-full p-2 rounded-lg resize-none ${darkMode ? 'bg-gray-600 text-white' : 'bg-gray-100 text-gray-800'}`,\n                rows: 2\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSaveEdit,\n                  className: \"px-3 py-1 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600\",\n                  children: /*#__PURE__*/_jsxDEV(Save, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCancelEdit,\n                  className: \"px-3 py-1 bg-gray-500 text-white rounded-lg text-sm hover:bg-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(X, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm leading-relaxed\",\n                children: message.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs ${message.sender === 'me' ? 'text-white/70' : darkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                  children: formatTime(message.timestamp)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${darkMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-pink-200'} backdrop-blur-sm rounded-b-2xl p-4 border-t`,\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: sendMessage,\n          className: \"flex items-end space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative\",\n            children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newMessage,\n              onChange: e => setNewMessage(e.target.value),\n              onKeyPress: handleKeyPress,\n              placeholder: \"Type your love message... \\uD83D\\uDC95\",\n              rows: 1,\n              className: `w-full px-4 py-3 rounded-xl resize-none focus:outline-none focus:ring-2 ${darkMode ? 'bg-gray-700 text-white border-gray-600 focus:ring-purple-500 placeholder-gray-400' : 'bg-pink-50 text-gray-800 border-pink-200 focus:ring-pink-300 placeholder-pink-400'} border-2 transition-all duration-300`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: !newMessage.trim(),\n            className: `px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center space-x-2 ${darkMode ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700' : 'bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600'} text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105`,\n            children: /*#__PURE__*/_jsxDEV(Send, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(Chat, \"O/8It6ZOTtBPMVvpzlAe2SzCjXc=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = Chat;\nexport default Chat;\nvar _c;\n$RefreshReg$(_c, \"Chat\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "useTheme", "Send", "Save", "X", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Cha<PERSON>", "_s", "user", "authUser", "darkMode", "messages", "setMessages", "id", "text", "sender", "timestamp", "Date", "now", "reactions", "newMessage", "setNewMessage", "editingMessage", "setEditingMessage", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "otherUserTyping", "setOtherUserTyping", "messagesEndRef", "otherUser", "name", "avatar", "handleSendMessage", "e", "preventDefault", "trim", "message", "handleSaveEdit", "map", "msg", "handleCancelEdit", "handleEditKeyPress", "key", "shift<PERSON>ey", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "target", "onKeyPress", "rows", "onClick", "size", "ref", "onSubmit", "sendMessage", "handleKeyPress", "placeholder", "type", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Chat.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Send, Save, X } from 'lucide-react';\n\nconst Chat = () => {\n  const { user: authUser } = useAuth();\n  const { darkMode } = useTheme();\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      text: \"Good morning, beautiful! ☀️\",\n      sender: 'other',\n      timestamp: new Date(Date.now() - 3600000),\n      reactions: ['❤️']\n    },\n    {\n      id: 2,\n      text: \"Good morning, love! Hope you slept well 💕\",\n      sender: 'me',\n      timestamp: new Date(Date.now() - 3500000),\n      reactions: []\n    }\n  ]);\n  \n  const [newMessage, setNewMessage] = useState('');\n  const [editingMessage, setEditingMessage] = useState(null);\n  const [editContent, setEditContent] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [otherUserTyping, setOtherUserTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  // Use authUser instead of user\n  const user = authUser;\n\n  // Mock other user data\n  const otherUser = {\n    name: \"My Love 💕\",\n    avatar: \"💖\"\n  };\n\n  const handleSendMessage = (e) => {\n    e.preventDefault();\n    if (newMessage.trim()) {\n      const message = {\n        id: Date.now(),\n        text: newMessage,\n        sender: 'me',\n        timestamp: new Date(),\n        reactions: []\n      };\n      setMessages([...messages, message]);\n      setNewMessage('');\n    }\n  };\n\n  const handleSaveEdit = () => {\n    setMessages(messages.map(msg => \n      msg.id === editingMessage ? { ...msg, text: editContent } : msg\n    ));\n    setEditingMessage(null);\n    setEditContent('');\n  };\n\n  const handleCancelEdit = () => {\n    setEditingMessage(null);\n    setEditContent('');\n  };\n\n  const handleEditKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSaveEdit();\n    } else if (e.key === 'Escape') {\n      handleCancelEdit();\n    }\n  };\n\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n\n  const formatTime = (date) => {\n    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 text-lg font-medium\">Loading your conversation...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`min-h-screen transition-colors duration-300 ${\n      darkMode \n        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' \n        : 'bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50'\n    }`}>\n      <div className=\"max-w-4xl mx-auto p-4 h-screen flex flex-col\">\n        {/* Chat Header */}\n        <div className={`${\n          darkMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-pink-200'\n        } backdrop-blur-sm rounded-t-2xl p-4 border-b shadow-lg`}>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center text-2xl animate-pulse\">\n                💕\n              </div>\n              <div>\n                <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>\n                  {otherUser.name}\n                </h2>\n                <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n                  {otherUserTyping ? 'Typing...' : 'Online'}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages Container */}\n        <div className={`flex-1 ${\n          darkMode ? 'bg-gray-800/50' : 'bg-white/50'\n        } backdrop-blur-sm overflow-y-auto p-4 space-y-4 custom-scrollbar`}>\n          {messages.map((message) => (\n            <div\n              key={message.id}\n              className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}\n            >\n              <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-lg ${\n                message.sender === 'me'\n                  ? darkMode \n                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'\n                    : 'bg-gradient-to-r from-pink-500 to-rose-500 text-white'\n                  : darkMode\n                    ? 'bg-gray-700 text-gray-100 border border-gray-600'\n                    : 'bg-white text-gray-800 border border-gray-200'\n              } relative group`}>\n                {editingMessage === message.id ? (\n                  <div className=\"space-y-2\">\n                    <textarea\n                      value={editContent}\n                      onChange={(e) => setEditContent(e.target.value)}\n                      onKeyPress={handleEditKeyPress}\n                      className={`w-full p-2 rounded-lg resize-none ${\n                        darkMode ? 'bg-gray-600 text-white' : 'bg-gray-100 text-gray-800'\n                      }`}\n                      rows={2}\n                    />\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={handleSaveEdit}\n                        className=\"px-3 py-1 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600\"\n                      >\n                        <Save size={14} />\n                      </button>\n                      <button\n                        onClick={handleCancelEdit}\n                        className=\"px-3 py-1 bg-gray-500 text-white rounded-lg text-sm hover:bg-gray-600\"\n                      >\n                        <X size={14} />\n                      </button>\n                    </div>\n                  </div>\n                ) : (\n                  <>\n                    <p className=\"text-sm leading-relaxed\">{message.text}</p>\n                    <div className=\"flex items-center justify-between mt-2\">\n                      <span className={`text-xs ${\n                        message.sender === 'me' \n                          ? 'text-white/70' \n                          : darkMode ? 'text-gray-400' : 'text-gray-500'\n                      }`}>\n                        {formatTime(message.timestamp)}\n                      </span>\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n          ))}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Message Input */}\n        <div className={`${\n          darkMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-pink-200'\n        } backdrop-blur-sm rounded-b-2xl p-4 border-t`}>\n          <form onSubmit={sendMessage} className=\"flex items-end space-x-3\">\n            <div className=\"flex-1 relative\">\n              <textarea\n                value={newMessage}\n                onChange={(e) => setNewMessage(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Type your love message... 💕\"\n                rows={1}\n                className={`w-full px-4 py-3 rounded-xl resize-none focus:outline-none focus:ring-2 ${\n                  darkMode \n                    ? 'bg-gray-700 text-white border-gray-600 focus:ring-purple-500 placeholder-gray-400'\n                    : 'bg-pink-50 text-gray-800 border-pink-200 focus:ring-pink-300 placeholder-pink-400'\n                } border-2 transition-all duration-300`}\n              />\n            </div>\n            <button\n              type=\"submit\"\n              disabled={!newMessage.trim()}\n              className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center space-x-2 ${\n                darkMode\n                  ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700'\n                  : 'bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600'\n              } text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105`}\n            >\n              <Send size={18} />\n            </button>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Chat;\n\n\n\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,IAAI,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7C,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC,IAAI,EAAEC;EAAS,CAAC,GAAGZ,OAAO,CAAC,CAAC;EACpC,MAAM;IAAEa;EAAS,CAAC,GAAGZ,QAAQ,CAAC,CAAC;EAC/B,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,CACvC;IACEmB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,6BAA6B;IACnCC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IACzCC,SAAS,EAAE,CAAC,IAAI;EAClB,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,4CAA4C;IAClDC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IACzCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMoC,cAAc,GAAGlC,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMY,IAAI,GAAGC,QAAQ;;EAErB;EACA,MAAMsB,SAAS,GAAG;IAChBC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIhB,UAAU,CAACiB,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,OAAO,GAAG;QACdzB,EAAE,EAAEI,IAAI,CAACC,GAAG,CAAC,CAAC;QACdJ,IAAI,EAAEM,UAAU;QAChBL,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBE,SAAS,EAAE;MACb,CAAC;MACDP,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE2B,OAAO,CAAC,CAAC;MACnCjB,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,cAAc,GAAGA,CAAA,KAAM;IAC3B3B,WAAW,CAACD,QAAQ,CAAC6B,GAAG,CAACC,GAAG,IAC1BA,GAAG,CAAC5B,EAAE,KAAKS,cAAc,GAAG;MAAE,GAAGmB,GAAG;MAAE3B,IAAI,EAAEU;IAAY,CAAC,GAAGiB,GAC9D,CAAC,CAAC;IACFlB,iBAAiB,CAAC,IAAI,CAAC;IACvBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMiB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BnB,iBAAiB,CAAC,IAAI,CAAC;IACvBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMkB,kBAAkB,GAAIR,CAAC,IAAK;IAChC,IAAIA,CAAC,CAACS,GAAG,KAAK,OAAO,IAAI,CAACT,CAAC,CAACU,QAAQ,EAAE;MACpCV,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBG,cAAc,CAAC,CAAC;IAClB,CAAC,MAAM,IAAIJ,CAAC,CAACS,GAAG,KAAK,QAAQ,EAAE;MAC7BF,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC;EAED/C,SAAS,CAAC,MAAM;IAAA,IAAAmD,qBAAA;IACd,CAAAA,qBAAA,GAAAhB,cAAc,CAACiB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACtC,QAAQ,CAAC,CAAC;EAEd,MAAMuC,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAO,IAAIlC,IAAI,CAACkC,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACtF,CAAC;EAED,IAAI5B,OAAO,EAAE;IACX,oBACEvB,OAAA;MAAKoD,SAAS,EAAC,mGAAmG;MAAAC,QAAA,eAChHrD,OAAA;QAAKoD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BrD,OAAA;UAAKoD,SAAS,EAAC;QAA6F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnHzD,OAAA;UAAGoD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEzD,OAAA;IAAKoD,SAAS,EAAE,+CACd7C,QAAQ,GACJ,8DAA8D,GAC9D,yDAAyD,EAC5D;IAAA8C,QAAA,eACDrD,OAAA;MAAKoD,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAE3DrD,OAAA;QAAKoD,SAAS,EAAE,GACd7C,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,wDACpB;QAAA8C,QAAA,eACvDrD,OAAA;UAAKoD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDrD,OAAA;YAAKoD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CrD,OAAA;cAAKoD,SAAS,EAAC,6HAA6H;cAAAC,QAAA,EAAC;YAE7I;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNzD,OAAA;cAAAqD,QAAA,gBACErD,OAAA;gBAAIoD,SAAS,EAAE,qBAAqB7C,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAG;gBAAA8C,QAAA,EAC7EzB,SAAS,CAACC;cAAI;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACLzD,OAAA;gBAAGoD,SAAS,EAAE,WAAW7C,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;gBAAA8C,QAAA,EACrE5B,eAAe,GAAG,WAAW,GAAG;cAAQ;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAKoD,SAAS,EAAE,UACd7C,QAAQ,GAAG,gBAAgB,GAAG,aAAa,kEACsB;QAAA8C,QAAA,GAChE7C,QAAQ,CAAC6B,GAAG,CAAEF,OAAO,iBACpBnC,OAAA;UAEEoD,SAAS,EAAE,QAAQjB,OAAO,CAACvB,MAAM,KAAK,IAAI,GAAG,aAAa,GAAG,eAAe,EAAG;UAAAyC,QAAA,eAE/ErD,OAAA;YAAKoD,SAAS,EAAE,wDACdjB,OAAO,CAACvB,MAAM,KAAK,IAAI,GACnBL,QAAQ,GACN,yDAAyD,GACzD,uDAAuD,GACzDA,QAAQ,GACN,kDAAkD,GAClD,+CAA+C,iBACrC;YAAA8C,QAAA,EACflC,cAAc,KAAKgB,OAAO,CAACzB,EAAE,gBAC5BV,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBrD,OAAA;gBACE0D,KAAK,EAAErC,WAAY;gBACnBsC,QAAQ,EAAG3B,CAAC,IAAKV,cAAc,CAACU,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;gBAChDG,UAAU,EAAErB,kBAAmB;gBAC/BY,SAAS,EAAE,qCACT7C,QAAQ,GAAG,wBAAwB,GAAG,2BAA2B,EAChE;gBACHuD,IAAI,EAAE;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACFzD,OAAA;gBAAKoD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BrD,OAAA;kBACE+D,OAAO,EAAE3B,cAAe;kBACxBgB,SAAS,EAAC,yEAAyE;kBAAAC,QAAA,eAEnFrD,OAAA,CAACH,IAAI;oBAACmE,IAAI,EAAE;kBAAG;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACTzD,OAAA;kBACE+D,OAAO,EAAExB,gBAAiB;kBAC1Ba,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjFrD,OAAA,CAACF,CAAC;oBAACkE,IAAI,EAAE;kBAAG;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENzD,OAAA,CAAAE,SAAA;cAAAmD,QAAA,gBACErD,OAAA;gBAAGoD,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAElB,OAAO,CAACxB;cAAI;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzDzD,OAAA;gBAAKoD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrDrD,OAAA;kBAAMoD,SAAS,EAAE,WACfjB,OAAO,CAACvB,MAAM,KAAK,IAAI,GACnB,eAAe,GACfL,QAAQ,GAAG,eAAe,GAAG,eAAe,EAC/C;kBAAA8C,QAAA,EACAN,UAAU,CAACZ,OAAO,CAACtB,SAAS;gBAAC;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,eACN;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GApDDtB,OAAO,CAACzB,EAAE;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqDZ,CACN,CAAC,eACFzD,OAAA;UAAKiE,GAAG,EAAEtC;QAAe;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAGNzD,OAAA;QAAKoD,SAAS,EAAE,GACd7C,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,8CAC9B;QAAA8C,QAAA,eAC7CrD,OAAA;UAAMkE,QAAQ,EAAEC,WAAY;UAACf,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBAC/DrD,OAAA;YAAKoD,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BrD,OAAA;cACE0D,KAAK,EAAEzC,UAAW;cAClB0C,QAAQ,EAAG3B,CAAC,IAAKd,aAAa,CAACc,CAAC,CAAC4B,MAAM,CAACF,KAAK,CAAE;cAC/CG,UAAU,EAAEO,cAAe;cAC3BC,WAAW,EAAC,wCAA8B;cAC1CP,IAAI,EAAE,CAAE;cACRV,SAAS,EAAE,2EACT7C,QAAQ,GACJ,mFAAmF,GACnF,mFAAmF;YACjD;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNzD,OAAA;YACEsE,IAAI,EAAC,QAAQ;YACbC,QAAQ,EAAE,CAACtD,UAAU,CAACiB,IAAI,CAAC,CAAE;YAC7BkB,SAAS,EAAE,4FACT7C,QAAQ,GACJ,sFAAsF,GACtF,kFAAkF,uGACgB;YAAA8C,QAAA,eAExGrD,OAAA,CAACJ,IAAI;cAACoE,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrD,EAAA,CA3NID,IAAI;EAAA,QACmBT,OAAO,EACbC,QAAQ;AAAA;AAAA6E,EAAA,GAFzBrE,IAAI;AA6NV,eAAeA,IAAI;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}