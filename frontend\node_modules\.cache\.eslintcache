[{"C:\\Users\\<USER>\\me and her project\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Login.js": "4", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\context\\AuthContext.js": "5", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\services\\api.js": "6", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Dashboard.js": "7", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Chat.js": "8", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Photos.js": "9", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Notes.js": "10", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\hooks\\useKeyboardShortcuts.js": "11", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\LoadingTransition.js": "12", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\SettingsPanel.js": "13", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\context\\ThemeContext.js": "14", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Home.js": "15"}, {"size": 535, "mtime": 1752911099796, "results": "16", "hashOfConfig": "17"}, {"size": 1669, "mtime": 1753689721096, "results": "18", "hashOfConfig": "17"}, {"size": 362, "mtime": 1752911100450, "results": "19", "hashOfConfig": "17"}, {"size": 15161, "mtime": 1753691326516, "results": "20", "hashOfConfig": "17"}, {"size": 1695, "mtime": 1752911881857, "results": "21", "hashOfConfig": "17"}, {"size": 1184, "mtime": 1752915277536, "results": "22", "hashOfConfig": "17"}, {"size": 10545, "mtime": 1753691332964, "results": "23", "hashOfConfig": "17"}, {"size": 8326, "mtime": 1753691308273, "results": "24", "hashOfConfig": "17"}, {"size": 23806, "mtime": 1753691485528, "results": "25", "hashOfConfig": "17"}, {"size": 14386, "mtime": 1753691385860, "results": "26", "hashOfConfig": "17"}, {"size": 609, "mtime": 1753690361136, "results": "27", "hashOfConfig": "17"}, {"size": 2867, "mtime": 1753690812117, "results": "28", "hashOfConfig": "17"}, {"size": 17126, "mtime": 1753690803844, "results": "29", "hashOfConfig": "17"}, {"size": 1165, "mtime": 1753690400453, "results": "30", "hashOfConfig": "17"}, {"size": 10499, "mtime": 1753691340878, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "we10pu", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\me and her project\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Login.js", ["77", "78", "79"], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Chat.js", ["80", "81", "82", "83", "84", "85", "86"], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Photos.js", ["87", "88", "89", "90"], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Notes.js", ["91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104"], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\hooks\\useKeyboardShortcuts.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\LoadingTransition.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\SettingsPanel.js", ["105", "106", "107", "108"], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\context\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Home.js", ["109", "110", "111", "112", "113", "114", "115", "116", "117", "118", "119", "120", "121", "122"], [], {"ruleId": "123", "severity": 1, "message": "124", "line": 36, "column": 9, "nodeType": "125", "messageId": "126", "endLine": 36, "endColumn": 24}, {"ruleId": "127", "severity": 2, "message": "128", "line": 160, "column": 25, "nodeType": "125", "messageId": "129", "endLine": 160, "endColumn": 38}, {"ruleId": "127", "severity": 2, "message": "128", "line": 160, "column": 45, "nodeType": "125", "messageId": "129", "endLine": 160, "endColumn": 58}, {"ruleId": "123", "severity": 1, "message": "130", "line": 32, "column": 9, "nodeType": "125", "messageId": "126", "endLine": 32, "endColumn": 13}, {"ruleId": "123", "severity": 1, "message": "131", "line": 34, "column": 9, "nodeType": "125", "messageId": "126", "endLine": 34, "endColumn": 26}, {"ruleId": "127", "severity": 2, "message": "132", "line": 79, "column": 7, "nodeType": "125", "messageId": "129", "endLine": 79, "endColumn": 14}, {"ruleId": "127", "severity": 2, "message": "133", "line": 108, "column": 20, "nodeType": "125", "messageId": "129", "endLine": 108, "endColumn": 29}, {"ruleId": "127", "severity": 2, "message": "134", "line": 111, "column": 20, "nodeType": "125", "messageId": "129", "endLine": 111, "endColumn": 35}, {"ruleId": "127", "severity": 2, "message": "135", "line": 186, "column": 27, "nodeType": "125", "messageId": "129", "endLine": 186, "endColumn": 38}, {"ruleId": "127", "severity": 2, "message": "136", "line": 191, "column": 29, "nodeType": "125", "messageId": "129", "endLine": 191, "endColumn": 43}, {"ruleId": "123", "severity": 1, "message": "137", "line": 4, "column": 10, "nodeType": "125", "messageId": "126", "endLine": 4, "endColumn": 14}, {"ruleId": "123", "severity": 1, "message": "138", "line": 4, "column": 16, "nodeType": "125", "messageId": "126", "endLine": 4, "endColumn": 22}, {"ruleId": "123", "severity": 1, "message": "139", "line": 4, "column": 24, "nodeType": "125", "messageId": "126", "endLine": 4, "endColumn": 28}, {"ruleId": "123", "severity": 1, "message": "140", "line": 4, "column": 30, "nodeType": "125", "messageId": "126", "endLine": 4, "endColumn": 34}, {"ruleId": "123", "severity": 1, "message": "141", "line": 4, "column": 46, "nodeType": "125", "messageId": "126", "endLine": 4, "endColumn": 50}, {"ruleId": "123", "severity": 1, "message": "142", "line": 8, "column": 11, "nodeType": "125", "messageId": "126", "endLine": 8, "endColumn": 19}, {"ruleId": "127", "severity": 2, "message": "143", "line": 51, "column": 30, "nodeType": "125", "messageId": "129", "endLine": 51, "endColumn": 38}, {"ruleId": "127", "severity": 2, "message": "144", "line": 59, "column": 7, "nodeType": "125", "messageId": "129", "endLine": 59, "endColumn": 24}, {"ruleId": "127", "severity": 2, "message": "143", "line": 73, "column": 30, "nodeType": "125", "messageId": "129", "endLine": 73, "endColumn": 38}, {"ruleId": "127", "severity": 2, "message": "143", "line": 88, "column": 13, "nodeType": "125", "messageId": "129", "endLine": 88, "endColumn": 21}, {"ruleId": "145", "severity": 2, "message": "146", "line": 122, "column": 12, "nodeType": "147", "messageId": "148", "endLine": 122, "endColumn": 22}, {"ruleId": "145", "severity": 2, "message": "149", "line": 139, "column": 14, "nodeType": "147", "messageId": "148", "endLine": 139, "endColumn": 21}, {"ruleId": "145", "severity": 2, "message": "146", "line": 158, "column": 16, "nodeType": "147", "messageId": "148", "endLine": 158, "endColumn": 26}, {"ruleId": "145", "severity": 2, "message": "146", "line": 207, "column": 12, "nodeType": "147", "messageId": "148", "endLine": 207, "endColumn": 22}, {"ruleId": "145", "severity": 2, "message": "150", "line": 307, "column": 14, "nodeType": "147", "messageId": "148", "endLine": 307, "endColumn": 18}, {"ruleId": "145", "severity": 2, "message": "151", "line": 311, "column": 14, "nodeType": "147", "messageId": "148", "endLine": 311, "endColumn": 22}, {"ruleId": "145", "severity": 2, "message": "152", "line": 332, "column": 20, "nodeType": "147", "messageId": "148", "endLine": 332, "endColumn": 24}, {"ruleId": "145", "severity": 2, "message": "153", "line": 339, "column": 20, "nodeType": "147", "messageId": "148", "endLine": 339, "endColumn": 21}, {"ruleId": "123", "severity": 1, "message": "154", "line": 6, "column": 11, "nodeType": "125", "messageId": "126", "endLine": 6, "endColumn": 19}, {"ruleId": "123", "severity": 1, "message": "155", "line": 6, "column": 21, "nodeType": "125", "messageId": "126", "endLine": 6, "endColumn": 27}, {"ruleId": "123", "severity": 1, "message": "156", "line": 14, "column": 10, "nodeType": "125", "messageId": "126", "endLine": 14, "endColumn": 18}, {"ruleId": "123", "severity": 1, "message": "157", "line": 14, "column": 20, "nodeType": "125", "messageId": "126", "endLine": 14, "endColumn": 31}, {"ruleId": "123", "severity": 1, "message": "158", "line": 4, "column": 27, "nodeType": "125", "messageId": "126", "endLine": 4, "endColumn": 40}, {"ruleId": "123", "severity": 1, "message": "159", "line": 4, "column": 42, "nodeType": "125", "messageId": "126", "endLine": 4, "endColumn": 48}, {"ruleId": "123", "severity": 1, "message": "160", "line": 4, "column": 50, "nodeType": "125", "messageId": "126", "endLine": 4, "endColumn": 57}, {"ruleId": "145", "severity": 2, "message": "161", "line": 52, "column": 14, "nodeType": "147", "messageId": "148", "endLine": 52, "endColumn": 26}, {"ruleId": "145", "severity": 2, "message": "162", "line": 93, "column": 16, "nodeType": "147", "messageId": "148", "endLine": 93, "endColumn": 22}, {"ruleId": "127", "severity": 2, "message": "163", "line": 111, "column": 15, "nodeType": "125", "messageId": "129", "endLine": 111, "endColumn": 21}, {"ruleId": "127", "severity": 2, "message": "164", "line": 111, "column": 22, "nodeType": "125", "messageId": "129", "endLine": 111, "endColumn": 34}, {"ruleId": "145", "severity": 2, "message": "165", "line": 160, "column": 14, "nodeType": "147", "messageId": "148", "endLine": 160, "endColumn": 23}, {"ruleId": "127", "severity": 2, "message": "166", "line": 160, "column": 48, "nodeType": "125", "messageId": "129", "endLine": 160, "endColumn": 59}, {"ruleId": "127", "severity": 2, "message": "166", "line": 166, "column": 10, "nodeType": "125", "messageId": "129", "endLine": 166, "endColumn": 21}, {"ruleId": "127", "severity": 2, "message": "167", "line": 190, "column": 30, "nodeType": "125", "messageId": "129", "endLine": 190, "endColumn": 40}, {"ruleId": "127", "severity": 2, "message": "168", "line": 190, "column": 43, "nodeType": "125", "messageId": "129", "endLine": 190, "endColumn": 54}, {"ruleId": "145", "severity": 2, "message": "169", "line": 209, "column": 14, "nodeType": "147", "messageId": "148", "endLine": 209, "endColumn": 20}, {"ruleId": "145", "severity": 2, "message": "170", "line": 221, "column": 14, "nodeType": "147", "messageId": "148", "endLine": 221, "endColumn": 18}, "no-unused-vars", "'FloatingElement' is assigned a value but never used.", "Identifier", "unusedVar", "no-undef", "'mousePosition' is not defined.", "undef", "'user' is assigned a value but never used.", "'handleSendMessage' is assigned a value but never used.", "'loading' is not defined.", "'otherUser' is not defined.", "'otherUserTyping' is not defined.", "'sendMessage' is not defined.", "'handleKeyPress' is not defined.", "'Plus' is defined but never used.", "'Search' is defined but never used.", "'Grid' is defined but never used.", "'List' is defined but never used.", "'Star' is defined but never used.", "'darkMode' is assigned a value but never used.", "'notesAPI' is not defined.", "'setShowCreateForm' is not defined.", "react/jsx-no-undef", "'StickyNote' is not defined.", "JSXIdentifier", "undefined", "'PenTool' is not defined.", "'User' is not defined.", "'Calendar' is not defined.", "'Save' is not defined.", "'X' is not defined.", "'Download' is defined but never used.", "'Trash2' is defined but never used.", "'autoSave' is assigned a value but never used.", "'setAutoSave' is assigned a value but never used.", "'MessageSquare' is defined but never used.", "'Camera' is defined but never used.", "'PenTool' is defined but never used.", "'ExternalLink' is not defined.", "'MapPin' is not defined.", "'quotes' is not defined.", "'currentQuote' is not defined.", "'RefreshCw' is not defined.", "'newsLoading' is not defined.", "'sportsNews' is not defined.", "'fashionNews' is not defined.", "'Coffee' is not defined.", "'Star' is not defined."]