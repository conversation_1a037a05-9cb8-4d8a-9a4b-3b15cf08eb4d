{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Notes.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Plus, Search, Edit3, Trash2, Heart, Star, Filter, StickyNote, PenTool, User, Calendar, Save, X } from 'lucide-react';\n\n// Mock API for notes\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst notesAPI = {\n  getNotes: () => Promise.resolve([]),\n  createNote: note => Promise.resolve({\n    ...note,\n    id: Date.now()\n  }),\n  updateNote: (id, note) => Promise.resolve(note),\n  deleteNote: id => Promise.resolve(true)\n};\nconst Notes = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    darkMode\n  } = useTheme();\n  const [notes, setNotes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [editingNote, setEditingNote] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterBy, setFilterBy] = useState('all');\n  const [newNote, setNewNote] = useState('');\n  const [showCreateForm, setShowCreateForm] = useState(true);\n  const filteredNotes = notes.filter(note => {\n    const matchesSearch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) || note.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterBy === 'all' || filterBy === 'mine' && note.author === (user === null || user === void 0 ? void 0 : user.name) || filterBy === 'theirs' && note.author !== (user === null || user === void 0 ? void 0 : user.name);\n    return matchesSearch && matchesFilter;\n  });\n  useEffect(() => {\n    loadNotes();\n  }, []);\n  const loadNotes = async () => {\n    setLoading(false);\n  };\n  const createNote = async e => {\n    e.preventDefault();\n    if (!newNote.content.trim()) return;\n    try {\n      const noteToCreate = {\n        ...newNote,\n        author: (user === null || user === void 0 ? void 0 : user.name) || 'Anonymous',\n        author_name: (user === null || user === void 0 ? void 0 : user.name) || 'Anonymous',\n        user_id: (user === null || user === void 0 ? void 0 : user.id) || 1,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n      const response = await notesAPI.createNote(noteToCreate);\n      setNotes(prev => [response, ...prev]); // Fix: use response directly\n      setNewNote({\n        title: '',\n        content: '',\n        tags: [],\n        color: 'yellow'\n      });\n      setShowCreateForm(false);\n    } catch (error) {\n      console.error('Error creating note:', error);\n      alert('Error creating note');\n    }\n  };\n  const updateNote = async (noteId, content) => {\n    try {\n      const updatedNote = {\n        ...notes.find(n => n.id === noteId),\n        content,\n        updated_at: new Date().toISOString()\n      };\n      const response = await notesAPI.updateNote(noteId, updatedNote);\n      setNotes(prev => prev.map(note => note.id === noteId ? response : note));\n      setEditingNote(null);\n    } catch (error) {\n      console.error('Error updating note:', error);\n      alert('Error updating note');\n    }\n  };\n  const deleteNote = async noteId => {\n    if (!window.confirm('Are you sure you want to delete this note?')) return;\n    try {\n      await notesAPI.deleteNote(noteId);\n      setNotes(prev => prev.filter(note => note.id !== noteId));\n    } catch (error) {\n      console.error('Error deleting note:', error);\n      alert('Error deleting note');\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      createNote(e);\n    }\n  };\n\n  // Note colors for variety\n  const noteColors = ['from-yellow-200 to-yellow-300 border-yellow-400', 'from-pink-200 to-pink-300 border-pink-400', 'from-blue-200 to-blue-300 border-blue-400', 'from-green-200 to-green-300 border-green-400', 'from-purple-200 to-purple-300 border-purple-400', 'from-orange-200 to-orange-300 border-orange-400', 'from-red-200 to-red-300 border-red-400', 'from-indigo-200 to-indigo-300 border-indigo-400'];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-pink-600 font-medium flex items-center justify-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(StickyNote, {\n          className: \"animate-pulse\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), \"Loading our love notes...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-br from-white via-pink-50 to-rose-50 rounded-2xl shadow-lg border-2 border-pink-200 p-6 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-2 right-2 text-2xl opacity-20 animate-pulse\",\n        children: \"\\uD83D\\uDC95\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-2 left-2 text-xl opacity-20 animate-bounce\",\n        children: \"\\u2728\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(PenTool, {\n            className: \"text-white\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-800 flex items-center gap-2\",\n          children: [\"Write a Love Note\", /*#__PURE__*/_jsxDEV(Heart, {\n            className: \"text-red-500 animate-pulse\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: createNote,\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: newNote,\n            onChange: e => setNewNote(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Pour your heart out... Write something beautiful for us \\uD83D\\uDC96\",\n            rows: 4,\n            className: \"w-full px-4 py-3 border-2 border-pink-300 rounded-xl focus:outline-none focus:ring-4 focus:ring-pink-200 focus:border-pink-500 resize-none transition-all duration-300 bg-white/80 backdrop-blur-sm text-gray-800 placeholder-pink-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-3 right-3 text-pink-400\",\n            children: /*#__PURE__*/_jsxDEV(StickyNote, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: !newNote.trim(),\n            className: \"px-6 py-3 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-xl hover:from-pink-600 hover:to-rose-600 focus:outline-none focus:ring-4 focus:ring-pink-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-medium flex items-center gap-2 shadow-lg hover:shadow-xl hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), \"Add Love Note\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 flex items-center gap-1\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Press Enter to add note, Shift+Enter for new line\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-4 items-center justify-between bg-white rounded-xl p-4 shadow-md border border-pink-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-pink-400\",\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search notes...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"pl-10 pr-4 py-2 border border-pink-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-300 focus:border-pink-400 bg-pink-50/50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            className: \"text-pink-500\",\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filterBy,\n            onChange: e => setFilterBy(e.target.value),\n            className: \"px-3 py-2 border border-pink-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-300 bg-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Notes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"mine\",\n              children: \"My Notes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"theirs\",\n              children: \"Their Notes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-pink-600 font-medium flex items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(StickyNote, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), filteredNotes.length, \" love notes\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-br from-white via-pink-50 to-rose-50 rounded-2xl shadow-lg border-2 border-pink-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(Heart, {\n            className: \"text-white\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-800\",\n          children: \"Our Love Notes Collection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), filteredNotes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-xl font-bold text-gray-600 mb-2\",\n          children: searchTerm ? 'No notes found' : 'No notes yet'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mb-6\",\n          children: searchTerm ? 'Try a different search term' : 'Write your first love note! ✍️'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center space-x-3 text-2xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-bounce\",\n            style: {\n              animationDelay: '0s'\n            },\n            children: \"\\uD83D\\uDC95\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-bounce\",\n            style: {\n              animationDelay: '0.2s'\n            },\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-bounce\",\n            style: {\n              animationDelay: '0.4s'\n            },\n            children: \"\\uD83D\\uDC96\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: filteredNotes.map((note, index) => /*#__PURE__*/_jsxDEV(NoteCard, {\n          note: note,\n          colorClass: noteColors[index % noteColors.length],\n          isOwner: note.user_id === user.id,\n          isEditing: editingNote === note.id,\n          onEdit: () => setEditingNote(note.id),\n          onCancelEdit: () => setEditingNote(null),\n          onSave: content => updateNote(note.id, content),\n          onDelete: () => deleteNote(note.id)\n        }, note.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n\n// Individual Note Card Component\n_s(Notes, \"ptjKOs1+88hFVkccIEKMmt6f4QY=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = Notes;\nconst NoteCard = ({\n  note,\n  colorClass,\n  isOwner,\n  isEditing,\n  onEdit,\n  onCancelEdit,\n  onSave,\n  onDelete\n}) => {\n  _s2();\n  const [editContent, setEditContent] = useState(note.content);\n  const handleSave = () => {\n    if (editContent.trim()) {\n      onSave(editContent.trim());\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && e.ctrlKey) {\n      e.preventDefault();\n      handleSave();\n    }\n    if (e.key === 'Escape') {\n      onCancelEdit();\n      setEditContent(note.content);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-gradient-to-br ${colorClass} rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:rotate-1 border-2 relative group min-h-[200px] flex flex-col`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-2 right-2 text-lg opacity-30 group-hover:opacity-60 transition-opacity\",\n      children: isOwner ? '💝' : '💕'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 mb-4\",\n      children: isEditing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: editContent,\n        onChange: e => setEditContent(e.target.value),\n        onKeyPress: handleKeyPress,\n        className: \"w-full h-32 p-3 bg-white/70 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-400 resize-none text-gray-800\",\n        autoFocus: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-800 whitespace-pre-wrap leading-relaxed text-sm font-medium\",\n        children: note.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-t border-white/50 pt-3 mt-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between text-xs text-gray-600 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(User, {\n            size: 12\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: note.author_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n            size: 12\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: new Date(note.created_at).toLocaleDateString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), note.updated_at !== note.created_at && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 mb-2 flex items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(Edit3, {\n          size: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Edited \", new Date(note.updated_at).toLocaleDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 11\n      }, this), isOwner && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end space-x-2 mt-2\",\n        children: isEditing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSave,\n            className: \"p-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center gap-1 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(Save, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 19\n            }, this), \"Save\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onCancelEdit,\n            className: \"p-1.5 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center gap-1 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(X, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 19\n            }, this), \"Cancel\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onEdit,\n            className: \"p-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors opacity-0 group-hover:opacity-100 flex items-center gap-1 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(Edit3, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this), \"Edit\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onDelete,\n            className: \"p-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100 flex items-center gap-1 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(Trash2, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 19\n            }, this), \"Delete\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this), isEditing && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500 mt-2 flex items-center gap-1\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Ctrl+Enter to save, Escape to cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 284,\n    columnNumber: 5\n  }, this);\n};\n_s2(NoteCard, \"Vu+w5YaFRxoQaV5Fqo2PSfof6gE=\");\n_c2 = NoteCard;\nexport default Notes;\nvar _c, _c2;\n$RefreshReg$(_c, \"Notes\");\n$RefreshReg$(_c2, \"NoteCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useTheme", "Plus", "Search", "Edit3", "Trash2", "Heart", "Star", "Filter", "StickyNote", "PenTool", "User", "Calendar", "Save", "X", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "notesAPI", "getNotes", "Promise", "resolve", "createNote", "note", "id", "Date", "now", "updateNote", "deleteNote", "Notes", "_s", "user", "darkMode", "notes", "setNotes", "loading", "setLoading", "editingNote", "setEditingNote", "searchTerm", "setSearchTerm", "filterBy", "setFilterBy", "newNote", "setNewNote", "showCreateForm", "setShowCreateForm", "filteredNotes", "filter", "matchesSearch", "title", "toLowerCase", "includes", "content", "matchesFilter", "author", "name", "loadNotes", "e", "preventDefault", "trim", "noteToCreate", "author_name", "user_id", "created_at", "toISOString", "updated_at", "response", "prev", "tags", "color", "error", "console", "alert", "noteId", "updatedNote", "find", "n", "map", "window", "confirm", "handleKeyPress", "key", "shift<PERSON>ey", "noteColors", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onSubmit", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "type", "disabled", "length", "style", "animationDelay", "index", "NoteCard", "colorClass", "isOwner", "isEditing", "onEdit", "onCancelEdit", "onSave", "onDelete", "_c", "_s2", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleSave", "ctrl<PERSON>ey", "autoFocus", "toLocaleDateString", "onClick", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Notes.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Plus, Search, Edit3, Trash2, Heart, Star, Filter, StickyNote, PenTool, User, Calendar, Save, X } from 'lucide-react';\n\n// Mock API for notes\nconst notesAPI = {\n  getNotes: () => Promise.resolve([]),\n  createNote: (note) => Promise.resolve({ ...note, id: Date.now() }),\n  updateNote: (id, note) => Promise.resolve(note),\n  deleteNote: (id) => Promise.resolve(true)\n};\n\nconst Notes = () => {\n  const { user } = useAuth();\n  const { darkMode } = useTheme();\n  const [notes, setNotes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [editingNote, setEditingNote] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterBy, setFilterBy] = useState('all');\n  const [newNote, setNewNote] = useState('');\n  const [showCreateForm, setShowCreateForm] = useState(true);\n\n  const filteredNotes = notes.filter(note => {\n    const matchesSearch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         note.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterBy === 'all' || \n                         (filterBy === 'mine' && note.author === user?.name) ||\n                         (filterBy === 'theirs' && note.author !== user?.name);\n    return matchesSearch && matchesFilter;\n  });\n\n  useEffect(() => {\n    loadNotes();\n  }, []);\n\n  const loadNotes = async () => {\n    setLoading(false);\n  };\n\n  const createNote = async (e) => {\n    e.preventDefault();\n    if (!newNote.content.trim()) return;\n\n    try {\n      const noteToCreate = {\n        ...newNote,\n        author: user?.name || 'Anonymous',\n        author_name: user?.name || 'Anonymous',\n        user_id: user?.id || 1,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n      const response = await notesAPI.createNote(noteToCreate);\n      setNotes(prev => [response, ...prev]); // Fix: use response directly\n      setNewNote({\n        title: '',\n        content: '',\n        tags: [],\n        color: 'yellow'\n      });\n      setShowCreateForm(false);\n    } catch (error) {\n      console.error('Error creating note:', error);\n      alert('Error creating note');\n    }\n  };\n\n  const updateNote = async (noteId, content) => {\n    try {\n      const updatedNote = {\n        ...notes.find(n => n.id === noteId),\n        content,\n        updated_at: new Date().toISOString()\n      };\n      const response = await notesAPI.updateNote(noteId, updatedNote);\n      setNotes(prev => prev.map(note => \n        note.id === noteId ? response : note\n      ));\n      setEditingNote(null);\n    } catch (error) {\n      console.error('Error updating note:', error);\n      alert('Error updating note');\n    }\n  };\n\n  const deleteNote = async (noteId) => {\n    if (!window.confirm('Are you sure you want to delete this note?')) return;\n\n    try {\n      await notesAPI.deleteNote(noteId);\n      setNotes(prev => prev.filter(note => note.id !== noteId));\n    } catch (error) {\n      console.error('Error deleting note:', error);\n      alert('Error deleting note');\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      createNote(e);\n    }\n  };\n\n\n\n  // Note colors for variety\n  const noteColors = [\n    'from-yellow-200 to-yellow-300 border-yellow-400',\n    'from-pink-200 to-pink-300 border-pink-400',\n    'from-blue-200 to-blue-300 border-blue-400',\n    'from-green-200 to-green-300 border-green-400',\n    'from-purple-200 to-purple-300 border-purple-400',\n    'from-orange-200 to-orange-300 border-orange-400',\n    'from-red-200 to-red-300 border-red-400',\n    'from-indigo-200 to-indigo-300 border-indigo-400',\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4\"></div>\n        <p className=\"text-pink-600 font-medium flex items-center justify-center gap-2\">\n          <StickyNote className=\"animate-pulse\" size={20} />\n          Loading our love notes...\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Create Note Section */}\n      <div className=\"bg-gradient-to-br from-white via-pink-50 to-rose-50 rounded-2xl shadow-lg border-2 border-pink-200 p-6 relative overflow-hidden\">\n        {/* Decorative elements */}\n        <div className=\"absolute top-2 right-2 text-2xl opacity-20 animate-pulse\">💕</div>\n        <div className=\"absolute bottom-2 left-2 text-xl opacity-20 animate-bounce\">✨</div>\n        \n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg\">\n            <PenTool className=\"text-white\" size={20} />\n          </div>\n          <h3 className=\"text-2xl font-bold text-gray-800 flex items-center gap-2\">\n            Write a Love Note\n            <Heart className=\"text-red-500 animate-pulse\" size={20} />\n          </h3>\n        </div>\n        \n        <form onSubmit={createNote} className=\"space-y-4\">\n          <div className=\"relative\">\n            <textarea\n              value={newNote}\n              onChange={(e) => setNewNote(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Pour your heart out... Write something beautiful for us 💖\"\n              rows={4}\n              className=\"w-full px-4 py-3 border-2 border-pink-300 rounded-xl focus:outline-none focus:ring-4 focus:ring-pink-200 focus:border-pink-500 resize-none transition-all duration-300 bg-white/80 backdrop-blur-sm text-gray-800 placeholder-pink-400\"\n            />\n            <div className=\"absolute bottom-3 right-3 text-pink-400\">\n              <StickyNote size={20} />\n            </div>\n          </div>\n          \n          <div className=\"flex items-center justify-between\">\n            <button\n              type=\"submit\"\n              disabled={!newNote.trim()}\n              className=\"px-6 py-3 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-xl hover:from-pink-600 hover:to-rose-600 focus:outline-none focus:ring-4 focus:ring-pink-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-medium flex items-center gap-2 shadow-lg hover:shadow-xl hover:scale-105\"\n            >\n              <Plus size={18} />\n              Add Love Note\n            </button>\n            <p className=\"text-sm text-gray-500 flex items-center gap-1\">\n              <span>Press Enter to add note, Shift+Enter for new line</span>\n            </p>\n          </div>\n        </form>\n      </div>\n\n      {/* Search and Filter Section */}\n      <div className=\"flex flex-wrap gap-4 items-center justify-between bg-white rounded-xl p-4 shadow-md border border-pink-100\">\n        <div className=\"flex items-center gap-4\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-pink-400\" size={18} />\n            <input\n              type=\"text\"\n              placeholder=\"Search notes...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 border border-pink-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-300 focus:border-pink-400 bg-pink-50/50\"\n            />\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <Filter className=\"text-pink-500\" size={18} />\n            <select\n              value={filterBy}\n              onChange={(e) => setFilterBy(e.target.value)}\n              className=\"px-3 py-2 border border-pink-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-300 bg-white\"\n            >\n              <option value=\"all\">All Notes</option>\n              <option value=\"mine\">My Notes</option>\n              <option value=\"theirs\">Their Notes</option>\n            </select>\n          </div>\n        </div>\n        \n        <div className=\"text-sm text-pink-600 font-medium flex items-center gap-1\">\n          <StickyNote size={16} />\n          {filteredNotes.length} love notes\n        </div>\n      </div>\n\n      {/* Notes Grid */}\n      <div className=\"bg-gradient-to-br from-white via-pink-50 to-rose-50 rounded-2xl shadow-lg border-2 border-pink-200 p-6\">\n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg\">\n            <Heart className=\"text-white\" size={20} />\n          </div>\n          <h3 className=\"text-2xl font-bold text-gray-800\">Our Love Notes Collection</h3>\n        </div>\n        \n        {filteredNotes.length === 0 ? (\n          <div className=\"text-center py-16\">\n            <div className=\"text-6xl mb-4\">📝</div>\n            <h4 className=\"text-xl font-bold text-gray-600 mb-2\">\n              {searchTerm ? 'No notes found' : 'No notes yet'}\n            </h4>\n            <p className=\"text-gray-500 mb-6\">\n              {searchTerm ? 'Try a different search term' : 'Write your first love note! ✍️'}\n            </p>\n            <div className=\"flex justify-center space-x-3 text-2xl\">\n              <span className=\"animate-bounce\" style={{animationDelay: '0s'}}>💕</span>\n              <span className=\"animate-bounce\" style={{animationDelay: '0.2s'}}>📝</span>\n              <span className=\"animate-bounce\" style={{animationDelay: '0.4s'}}>💖</span>\n            </div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredNotes.map((note, index) => (\n              <NoteCard\n                key={note.id}\n                note={note}\n                colorClass={noteColors[index % noteColors.length]}\n                isOwner={note.user_id === user.id}\n                isEditing={editingNote === note.id}\n                onEdit={() => setEditingNote(note.id)}\n                onCancelEdit={() => setEditingNote(null)}\n                onSave={(content) => updateNote(note.id, content)}\n                onDelete={() => deleteNote(note.id)}\n              />\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Individual Note Card Component\nconst NoteCard = ({ note, colorClass, isOwner, isEditing, onEdit, onCancelEdit, onSave, onDelete }) => {\n  const [editContent, setEditContent] = useState(note.content);\n\n  const handleSave = () => {\n    if (editContent.trim()) {\n      onSave(editContent.trim());\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && e.ctrlKey) {\n      e.preventDefault();\n      handleSave();\n    }\n    if (e.key === 'Escape') {\n      onCancelEdit();\n      setEditContent(note.content);\n    }\n  };\n\n  return (\n    <div className={`bg-gradient-to-br ${colorClass} rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:rotate-1 border-2 relative group min-h-[200px] flex flex-col`}>\n      {/* Decorative corner */}\n      <div className=\"absolute top-2 right-2 text-lg opacity-30 group-hover:opacity-60 transition-opacity\">\n        {isOwner ? '💝' : '💕'}\n      </div>\n      \n      {/* Note content */}\n      <div className=\"flex-1 mb-4\">\n        {isEditing ? (\n          <textarea\n            value={editContent}\n            onChange={(e) => setEditContent(e.target.value)}\n            onKeyPress={handleKeyPress}\n            className=\"w-full h-32 p-3 bg-white/70 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-400 resize-none text-gray-800\"\n            autoFocus\n          />\n        ) : (\n          <p className=\"text-gray-800 whitespace-pre-wrap leading-relaxed text-sm font-medium\">\n            {note.content}\n          </p>\n        )}\n      </div>\n      \n      {/* Note metadata */}\n      <div className=\"border-t border-white/50 pt-3 mt-auto\">\n        <div className=\"flex items-center justify-between text-xs text-gray-600 mb-2\">\n          <div className=\"flex items-center gap-1\">\n            <User size={12} />\n            <span className=\"font-medium\">{note.author_name}</span>\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <Calendar size={12} />\n            <span>{new Date(note.created_at).toLocaleDateString()}</span>\n          </div>\n        </div>\n        \n        {note.updated_at !== note.created_at && (\n          <div className=\"text-xs text-gray-500 mb-2 flex items-center gap-1\">\n            <Edit3 size={10} />\n            <span>Edited {new Date(note.updated_at).toLocaleDateString()}</span>\n          </div>\n        )}\n        \n        {/* Action buttons */}\n        {isOwner && (\n          <div className=\"flex justify-end space-x-2 mt-2\">\n            {isEditing ? (\n              <>\n                <button\n                  onClick={handleSave}\n                  className=\"p-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center gap-1 text-xs\"\n                >\n                  <Save size={12} />\n                  Save\n                </button>\n                <button\n                  onClick={onCancelEdit}\n                  className=\"p-1.5 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center gap-1 text-xs\"\n                >\n                  <X size={12} />\n                  Cancel\n                </button>\n              </>\n            ) : (\n              <>\n                <button\n                  onClick={onEdit}\n                  className=\"p-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors opacity-0 group-hover:opacity-100 flex items-center gap-1 text-xs\"\n                >\n                  <Edit3 size={12} />\n                  Edit\n                </button>\n                <button\n                  onClick={onDelete}\n                  className=\"p-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100 flex items-center gap-1 text-xs\"\n                >\n                  <Trash2 size={12} />\n                  Delete\n                </button>\n              </>\n            )}\n          </div>\n        )}\n        \n        {isEditing && (\n          <p className=\"text-xs text-gray-500 mt-2 flex items-center gap-1\">\n            <span>Ctrl+Enter to save, Escape to cancel</span>\n          </p>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Notes;\n\n\n\n\n\n\n\n\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;;AAE7H;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,QAAQ,GAAG;EACfC,QAAQ,EAAEA,CAAA,KAAMC,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC;EACnCC,UAAU,EAAGC,IAAI,IAAKH,OAAO,CAACC,OAAO,CAAC;IAAE,GAAGE,IAAI;IAAEC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC;EAAE,CAAC,CAAC;EAClEC,UAAU,EAAEA,CAACH,EAAE,EAAED,IAAI,KAAKH,OAAO,CAACC,OAAO,CAACE,IAAI,CAAC;EAC/CK,UAAU,EAAGJ,EAAE,IAAKJ,OAAO,CAACC,OAAO,CAAC,IAAI;AAC1C,CAAC;AAED,MAAMQ,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC;EAAK,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEiC;EAAS,CAAC,GAAGhC,QAAQ,CAAC,CAAC;EAC/B,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAMkD,aAAa,GAAGd,KAAK,CAACe,MAAM,CAACzB,IAAI,IAAI;IACzC,MAAM0B,aAAa,GAAG1B,IAAI,CAAC2B,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACb,UAAU,CAACY,WAAW,CAAC,CAAC,CAAC,IAC5D5B,IAAI,CAAC8B,OAAO,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACb,UAAU,CAACY,WAAW,CAAC,CAAC,CAAC;IAClF,MAAMG,aAAa,GAAGb,QAAQ,KAAK,KAAK,IAClBA,QAAQ,KAAK,MAAM,IAAIlB,IAAI,CAACgC,MAAM,MAAKxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,IAAI,CAAC,IAClDf,QAAQ,KAAK,QAAQ,IAAIlB,IAAI,CAACgC,MAAM,MAAKxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,IAAI,CAAC;IAC1E,OAAOP,aAAa,IAAIK,aAAa;EACvC,CAAC,CAAC;EAEFxD,SAAS,CAAC,MAAM;IACd2D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BrB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMd,UAAU,GAAG,MAAOoC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAChB,OAAO,CAACU,OAAO,CAACO,IAAI,CAAC,CAAC,EAAE;IAE7B,IAAI;MACF,MAAMC,YAAY,GAAG;QACnB,GAAGlB,OAAO;QACVY,MAAM,EAAE,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,IAAI,KAAI,WAAW;QACjCM,WAAW,EAAE,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyB,IAAI,KAAI,WAAW;QACtCO,OAAO,EAAE,CAAAhC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEP,EAAE,KAAI,CAAC;QACtBwC,UAAU,EAAE,IAAIvC,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC;QACpCC,UAAU,EAAE,IAAIzC,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC;MACrC,CAAC;MACD,MAAME,QAAQ,GAAG,MAAMjD,QAAQ,CAACI,UAAU,CAACuC,YAAY,CAAC;MACxD3B,QAAQ,CAACkC,IAAI,IAAI,CAACD,QAAQ,EAAE,GAAGC,IAAI,CAAC,CAAC,CAAC,CAAC;MACvCxB,UAAU,CAAC;QACTM,KAAK,EAAE,EAAE;QACTG,OAAO,EAAE,EAAE;QACXgB,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE;MACT,CAAC,CAAC;MACFxB,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CE,KAAK,CAAC,qBAAqB,CAAC;IAC9B;EACF,CAAC;EAED,MAAM9C,UAAU,GAAG,MAAAA,CAAO+C,MAAM,EAAErB,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMsB,WAAW,GAAG;QAClB,GAAG1C,KAAK,CAAC2C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACrD,EAAE,KAAKkD,MAAM,CAAC;QACnCrB,OAAO;QACPa,UAAU,EAAE,IAAIzC,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC;MACrC,CAAC;MACD,MAAME,QAAQ,GAAG,MAAMjD,QAAQ,CAACS,UAAU,CAAC+C,MAAM,EAAEC,WAAW,CAAC;MAC/DzC,QAAQ,CAACkC,IAAI,IAAIA,IAAI,CAACU,GAAG,CAACvD,IAAI,IAC5BA,IAAI,CAACC,EAAE,KAAKkD,MAAM,GAAGP,QAAQ,GAAG5C,IAClC,CAAC,CAAC;MACFe,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CE,KAAK,CAAC,qBAAqB,CAAC;IAC9B;EACF,CAAC;EAED,MAAM7C,UAAU,GAAG,MAAO8C,MAAM,IAAK;IACnC,IAAI,CAACK,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAM9D,QAAQ,CAACU,UAAU,CAAC8C,MAAM,CAAC;MACjCxC,QAAQ,CAACkC,IAAI,IAAIA,IAAI,CAACpB,MAAM,CAACzB,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKkD,MAAM,CAAC,CAAC;IAC3D,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CE,KAAK,CAAC,qBAAqB,CAAC;IAC9B;EACF,CAAC;EAED,MAAMQ,cAAc,GAAIvB,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACwB,GAAG,KAAK,OAAO,IAAI,CAACxB,CAAC,CAACyB,QAAQ,EAAE;MACpCzB,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBrC,UAAU,CAACoC,CAAC,CAAC;IACf;EACF,CAAC;;EAID;EACA,MAAM0B,UAAU,GAAG,CACjB,iDAAiD,EACjD,2CAA2C,EAC3C,2CAA2C,EAC3C,8CAA8C,EAC9C,iDAAiD,EACjD,iDAAiD,EACjD,wCAAwC,EACxC,iDAAiD,CAClD;EAED,IAAIjD,OAAO,EAAE;IACX,oBACEpB,OAAA;MAAKsE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvE,OAAA;QAAKsE,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnG3E,OAAA;QAAGsE,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC7EvE,OAAA,CAACP,UAAU;UAAC6E,SAAS,EAAC,eAAe;UAACM,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,6BAEpD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV;EAEA,oBACE3E,OAAA;IAAKsE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBvE,OAAA;MAAKsE,SAAS,EAAC,iIAAiI;MAAAC,QAAA,gBAE9IvE,OAAA;QAAKsE,SAAS,EAAC,0DAA0D;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClF3E,OAAA;QAAKsE,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEnF3E,OAAA;QAAKsE,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CvE,OAAA;UAAKsE,SAAS,EAAC,8GAA8G;UAAAC,QAAA,eAC3HvE,OAAA,CAACN,OAAO;YAAC4E,SAAS,EAAC,YAAY;YAACM,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACN3E,OAAA;UAAIsE,SAAS,EAAC,0DAA0D;UAAAC,QAAA,GAAC,mBAEvE,eAAAvE,OAAA,CAACV,KAAK;YAACgF,SAAS,EAAC,4BAA4B;YAACM,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN3E,OAAA;QAAM6E,QAAQ,EAAEtE,UAAW;QAAC+D,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAC/CvE,OAAA;UAAKsE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBvE,OAAA;YACE8E,KAAK,EAAElD,OAAQ;YACfmD,QAAQ,EAAGpC,CAAC,IAAKd,UAAU,CAACc,CAAC,CAACqC,MAAM,CAACF,KAAK,CAAE;YAC5CG,UAAU,EAAEf,cAAe;YAC3BgB,WAAW,EAAC,sEAA4D;YACxEC,IAAI,EAAE,CAAE;YACRb,SAAS,EAAC;UAAwO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnP,CAAC,eACF3E,OAAA;YAAKsE,SAAS,EAAC,yCAAyC;YAAAC,QAAA,eACtDvE,OAAA,CAACP,UAAU;cAACmF,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3E,OAAA;UAAKsE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvE,OAAA;YACEoF,IAAI,EAAC,QAAQ;YACbC,QAAQ,EAAE,CAACzD,OAAO,CAACiB,IAAI,CAAC,CAAE;YAC1ByB,SAAS,EAAC,gUAAgU;YAAAC,QAAA,gBAE1UvE,OAAA,CAACd,IAAI;cAAC0F,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3E,OAAA;YAAGsE,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC1DvE,OAAA;cAAAuE,QAAA,EAAM;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN3E,OAAA;MAAKsE,SAAS,EAAC,4GAA4G;MAAAC,QAAA,gBACzHvE,OAAA;QAAKsE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCvE,OAAA;UAAKsE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBvE,OAAA,CAACb,MAAM;YAACmF,SAAS,EAAC,kEAAkE;YAACM,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjG3E,OAAA;YACEoF,IAAI,EAAC,MAAM;YACXF,WAAW,EAAC,iBAAiB;YAC7BJ,KAAK,EAAEtD,UAAW;YAClBuD,QAAQ,EAAGpC,CAAC,IAAKlB,aAAa,CAACkB,CAAC,CAACqC,MAAM,CAACF,KAAK,CAAE;YAC/CR,SAAS,EAAC;UAA2I;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3E,OAAA;UAAKsE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCvE,OAAA,CAACR,MAAM;YAAC8E,SAAS,EAAC,eAAe;YAACM,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9C3E,OAAA;YACE8E,KAAK,EAAEpD,QAAS;YAChBqD,QAAQ,EAAGpC,CAAC,IAAKhB,WAAW,CAACgB,CAAC,CAACqC,MAAM,CAACF,KAAK,CAAE;YAC7CR,SAAS,EAAC,0GAA0G;YAAAC,QAAA,gBAEpHvE,OAAA;cAAQ8E,KAAK,EAAC,KAAK;cAAAP,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC3E,OAAA;cAAQ8E,KAAK,EAAC,MAAM;cAAAP,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC3E,OAAA;cAAQ8E,KAAK,EAAC,QAAQ;cAAAP,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN3E,OAAA;QAAKsE,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxEvE,OAAA,CAACP,UAAU;UAACmF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACvB3C,aAAa,CAACsD,MAAM,EAAC,aACxB;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3E,OAAA;MAAKsE,SAAS,EAAC,wGAAwG;MAAAC,QAAA,gBACrHvE,OAAA;QAAKsE,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CvE,OAAA;UAAKsE,SAAS,EAAC,6GAA6G;UAAAC,QAAA,eAC1HvE,OAAA,CAACV,KAAK;YAACgF,SAAS,EAAC,YAAY;YAACM,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACN3E,OAAA;UAAIsE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,EAEL3C,aAAa,CAACsD,MAAM,KAAK,CAAC,gBACzBtF,OAAA;QAAKsE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCvE,OAAA;UAAKsE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC3E,OAAA;UAAIsE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EACjD/C,UAAU,GAAG,gBAAgB,GAAG;QAAc;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACL3E,OAAA;UAAGsE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC9B/C,UAAU,GAAG,6BAA6B,GAAG;QAAgC;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eACJ3E,OAAA;UAAKsE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDvE,OAAA;YAAMsE,SAAS,EAAC,gBAAgB;YAACiB,KAAK,EAAE;cAACC,cAAc,EAAE;YAAI,CAAE;YAAAjB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzE3E,OAAA;YAAMsE,SAAS,EAAC,gBAAgB;YAACiB,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3E3E,OAAA;YAAMsE,SAAS,EAAC,gBAAgB;YAACiB,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN3E,OAAA;QAAKsE,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFvC,aAAa,CAAC+B,GAAG,CAAC,CAACvD,IAAI,EAAEiF,KAAK,kBAC7BzF,OAAA,CAAC0F,QAAQ;UAEPlF,IAAI,EAAEA,IAAK;UACXmF,UAAU,EAAEtB,UAAU,CAACoB,KAAK,GAAGpB,UAAU,CAACiB,MAAM,CAAE;UAClDM,OAAO,EAAEpF,IAAI,CAACwC,OAAO,KAAKhC,IAAI,CAACP,EAAG;UAClCoF,SAAS,EAAEvE,WAAW,KAAKd,IAAI,CAACC,EAAG;UACnCqF,MAAM,EAAEA,CAAA,KAAMvE,cAAc,CAACf,IAAI,CAACC,EAAE,CAAE;UACtCsF,YAAY,EAAEA,CAAA,KAAMxE,cAAc,CAAC,IAAI,CAAE;UACzCyE,MAAM,EAAG1D,OAAO,IAAK1B,UAAU,CAACJ,IAAI,CAACC,EAAE,EAAE6B,OAAO,CAAE;UAClD2D,QAAQ,EAAEA,CAAA,KAAMpF,UAAU,CAACL,IAAI,CAACC,EAAE;QAAE,GAR/BD,IAAI,CAACC,EAAE;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASb,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA5D,EAAA,CAxPMD,KAAK;EAAA,QACQ9B,OAAO,EACHC,QAAQ;AAAA;AAAAiH,EAAA,GAFzBpF,KAAK;AAyPX,MAAM4E,QAAQ,GAAGA,CAAC;EAAElF,IAAI;EAAEmF,UAAU;EAAEC,OAAO;EAAEC,SAAS;EAAEC,MAAM;EAAEC,YAAY;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAE,GAAA;EACrG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvH,QAAQ,CAAC0B,IAAI,CAAC8B,OAAO,CAAC;EAE5D,MAAMgE,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIF,WAAW,CAACvD,IAAI,CAAC,CAAC,EAAE;MACtBmD,MAAM,CAACI,WAAW,CAACvD,IAAI,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMqB,cAAc,GAAIvB,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACwB,GAAG,KAAK,OAAO,IAAIxB,CAAC,CAAC4D,OAAO,EAAE;MAClC5D,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB0D,UAAU,CAAC,CAAC;IACd;IACA,IAAI3D,CAAC,CAACwB,GAAG,KAAK,QAAQ,EAAE;MACtB4B,YAAY,CAAC,CAAC;MACdM,cAAc,CAAC7F,IAAI,CAAC8B,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,oBACEtC,OAAA;IAAKsE,SAAS,EAAE,qBAAqBqB,UAAU,oKAAqK;IAAApB,QAAA,gBAElNvE,OAAA;MAAKsE,SAAS,EAAC,qFAAqF;MAAAC,QAAA,EACjGqB,OAAO,GAAG,IAAI,GAAG;IAAI;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAGN3E,OAAA;MAAKsE,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzBsB,SAAS,gBACR7F,OAAA;QACE8E,KAAK,EAAEsB,WAAY;QACnBrB,QAAQ,EAAGpC,CAAC,IAAK0D,cAAc,CAAC1D,CAAC,CAACqC,MAAM,CAACF,KAAK,CAAE;QAChDG,UAAU,EAAEf,cAAe;QAC3BI,SAAS,EAAC,6IAA6I;QACvJkC,SAAS;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAEF3E,OAAA;QAAGsE,SAAS,EAAC,uEAAuE;QAAAC,QAAA,EACjF/D,IAAI,CAAC8B;MAAO;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IACJ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN3E,OAAA;MAAKsE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDvE,OAAA;QAAKsE,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3EvE,OAAA;UAAKsE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCvE,OAAA,CAACL,IAAI;YAACiF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClB3E,OAAA;YAAMsE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE/D,IAAI,CAACuC;UAAW;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACN3E,OAAA;UAAKsE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCvE,OAAA,CAACJ,QAAQ;YAACgF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtB3E,OAAA;YAAAuE,QAAA,EAAO,IAAI7D,IAAI,CAACF,IAAI,CAACyC,UAAU,CAAC,CAACwD,kBAAkB,CAAC;UAAC;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELnE,IAAI,CAAC2C,UAAU,KAAK3C,IAAI,CAACyC,UAAU,iBAClCjD,OAAA;QAAKsE,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjEvE,OAAA,CAACZ,KAAK;UAACwF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnB3E,OAAA;UAAAuE,QAAA,GAAM,SAAO,EAAC,IAAI7D,IAAI,CAACF,IAAI,CAAC2C,UAAU,CAAC,CAACsD,kBAAkB,CAAC,CAAC;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CACN,EAGAiB,OAAO,iBACN5F,OAAA;QAAKsE,SAAS,EAAC,iCAAiC;QAAAC,QAAA,EAC7CsB,SAAS,gBACR7F,OAAA,CAAAE,SAAA;UAAAqE,QAAA,gBACEvE,OAAA;YACE0G,OAAO,EAAEJ,UAAW;YACpBhC,SAAS,EAAC,+GAA+G;YAAAC,QAAA,gBAEzHvE,OAAA,CAACH,IAAI;cAAC+E,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3E,OAAA;YACE0G,OAAO,EAAEX,YAAa;YACtBzB,SAAS,EAAC,6GAA6G;YAAAC,QAAA,gBAEvHvE,OAAA,CAACF,CAAC;cAAC8E,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEH3E,OAAA,CAAAE,SAAA;UAAAqE,QAAA,gBACEvE,OAAA;YACE0G,OAAO,EAAEZ,MAAO;YAChBxB,SAAS,EAAC,+IAA+I;YAAAC,QAAA,gBAEzJvE,OAAA,CAACZ,KAAK;cAACwF,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAErB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3E,OAAA;YACE0G,OAAO,EAAET,QAAS;YAClB3B,SAAS,EAAC,6IAA6I;YAAAC,QAAA,gBAEvJvE,OAAA,CAACX,MAAM;cAACuF,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAEAkB,SAAS,iBACR7F,OAAA;QAAGsE,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eAC/DvE,OAAA;UAAAuE,QAAA,EAAM;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACwB,GAAA,CAjHIT,QAAQ;AAAAiB,GAAA,GAARjB,QAAQ;AAmHd,eAAe5E,KAAK;AAAC,IAAAoF,EAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}