import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { Send, Save, X } from 'lucide-react';

const Chat = () => {
  const { user: authUser } = useAuth();
  const { darkMode } = useTheme();
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Good morning, beautiful! ☀️",
      sender: 'other',
      timestamp: new Date(Date.now() - 3600000),
      reactions: ['❤️']
    },
    {
      id: 2,
      text: "Good morning, love! Hope you slept well 💕",
      sender: 'me',
      timestamp: new Date(Date.now() - 3500000),
      reactions: []
    }
  ]);
  
  const [newMessage, setNewMessage] = useState('');
  const [editingMessage, setEditingMessage] = useState(null);
  const [editContent, setEditContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const messagesEndRef = useRef(null);

  // Use authUser instead of user
  const user = authUser;

  // Mock other user data
  const otherUser = {
    name: "My Love 💕",
    avatar: "💖"
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (newMessage.trim()) {
      const message = {
        id: Date.now(),
        text: newMessage,
        sender: 'me',
        timestamp: new Date(),
        reactions: []
      };
      setMessages([...messages, message]);
      setNewMessage('');
    }
  };

  const handleSaveEdit = () => {
    setMessages(messages.map(msg => 
      msg.id === editingMessage ? { ...msg, text: editContent } : msg
    ));
    setEditingMessage(null);
    setEditContent('');
  };

  const handleCancelEdit = () => {
    setEditingMessage(null);
    setEditContent('');
  };

  const handleEditKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const sendMessage = handleSendMessage;

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg font-medium">Loading your conversation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      darkMode 
        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' 
        : 'bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50'
    }`}>
      <div className="max-w-4xl mx-auto p-4 h-screen flex flex-col">
        {/* Chat Header */}
        <div className={`${
          darkMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-pink-200'
        } backdrop-blur-sm rounded-t-2xl p-4 border-b shadow-lg`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center text-2xl animate-pulse">
                💕
              </div>
              <div>
                <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                  {otherUser.name}
                </h2>
                <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {otherUserTyping ? 'Typing...' : 'Online'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Messages Container */}
        <div className={`flex-1 ${
          darkMode ? 'bg-gray-800/50' : 'bg-white/50'
        } backdrop-blur-sm overflow-y-auto p-4 space-y-4 custom-scrollbar`}>
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-lg ${
                message.sender === 'me'
                  ? darkMode 
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'
                    : 'bg-gradient-to-r from-pink-500 to-rose-500 text-white'
                  : darkMode
                    ? 'bg-gray-700 text-gray-100 border border-gray-600'
                    : 'bg-white text-gray-800 border border-gray-200'
              } relative group`}>
                {editingMessage === message.id ? (
                  <div className="space-y-2">
                    <textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      onKeyDown={handleEditKeyPress}
                      className={`w-full p-2 rounded-lg resize-none ${
                        darkMode ? 'bg-gray-600 text-white' : 'bg-gray-100 text-gray-800'
                      }`}
                      rows={2}
                    />
                    <div className="flex space-x-2">
                      <button
                        onClick={handleSaveEdit}
                        className="px-3 py-1 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600"
                      >
                        <Save size={14} />
                      </button>
                      <button
                        onClick={handleCancelEdit}
                        className="px-3 py-1 bg-gray-500 text-white rounded-lg text-sm hover:bg-gray-600"
                      >
                        <X size={14} />
                      </button>
                    </div>
                  </div>
                ) : (
                  <>
                    <p className="text-sm leading-relaxed">{message.text}</p>
                    <div className="flex items-center justify-between mt-2">
                      <span className={`text-xs ${
                        message.sender === 'me' 
                          ? 'text-white/70' 
                          : darkMode ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {formatTime(message.timestamp)}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <div className={`${
          darkMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-pink-200'
        } backdrop-blur-sm rounded-b-2xl p-4 border-t`}>
          <form onSubmit={sendMessage} className="flex items-end space-x-3">
            <div className="flex-1 relative">
              <textarea
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Type your love message... 💕"
                rows={1}
                className={`w-full px-4 py-3 rounded-xl resize-none focus:outline-none focus:ring-2 ${
                  darkMode 
                    ? 'bg-gray-700 text-white border-gray-600 focus:ring-purple-500 placeholder-gray-400'
                    : 'bg-pink-50 text-gray-800 border-pink-200 focus:ring-pink-300 placeholder-pink-400'
                } border-2 transition-all duration-300`}
              />
            </div>
            <button
              type="submit"
              disabled={!newMessage.trim()}
              className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center space-x-2 ${
                darkMode
                  ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700'
                  : 'bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600'
              } text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105`}
            >
              <Send size={18} />
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Chat;





