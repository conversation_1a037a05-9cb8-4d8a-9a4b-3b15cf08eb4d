import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { Calendar, Heart, MessageSquare, Camera, PenTool, ExternalLink, MapPin, RefreshCw, Coffee, Star } from 'lucide-react';

const Home = () => {
  const { user } = useAuth();
  const { darkMode } = useTheme();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isHisAccount, setIsHisAccount] = useState(false);
  const [newsLoading, setNewsLoading] = useState(false);
  const [currentQuote, setCurrentQuote] = useState(0);

  // Love quotes array
  const quotes = [
    "Every love story is beautiful, but ours is my favorite.",
    "You are my today and all of my tomorrows.",
    "In a sea of people, my eyes will always search for you.",
    "You are my sunshine on a cloudy day.",
    "Together is a wonderful place to be.",
    "You make my heart smile.",
    "Love is not about how many days, months, or years you have been together. It's about how much you love each other every single day."
  ];

  // Mock news data
  const sportsNews = [
    {
      id: 1,
      title: "Local Basketball Team Wins Championship",
      summary: "Amazing victory in the finals last night!",
      source: "Sports Daily",
      image: "🏀"
    },
    {
      id: 2,
      title: "Football Season Kicks Off",
      summary: "Exciting matches scheduled for this weekend.",
      source: "Sports Central",
      image: "⚽"
    }
  ];

  const fashionNews = [
    {
      id: 1,
      title: "Spring Fashion Trends 2024",
      summary: "Discover the latest styles and colors for the season.",
      source: "Fashion Weekly",
      image: "👗"
    },
    {
      id: 2,
      title: "Sustainable Fashion Movement",
      summary: "Eco-friendly brands leading the change.",
      source: "Style Magazine",
      image: "🌱"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    setIsHisAccount(user?.name?.toLowerCase().includes('alex') || user?.email?.includes('alex'));
  }, [user]);

  useEffect(() => {
    const quoteTimer = setInterval(() => {
      setCurrentQuote(prev => (prev + 1) % quotes.length);
    }, 10000); // Change quote every 10 seconds
    return () => clearInterval(quoteTimer);
  }, [quotes.length]);

  const NewsCard = ({ article, type }) => (
    <div className={`${
      darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'
    } backdrop-blur-sm rounded-xl p-4 border hover:shadow-lg transition-all duration-300 group cursor-pointer`}>
      <div className="flex items-start space-x-3">
        <div className="text-2xl">{article.image}</div>
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              type === 'sports' 
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300'
                : 'bg-pink-100 text-pink-800 dark:bg-pink-900/50 dark:text-pink-300'
            }`}>
              {article.category}
            </span>
            <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              {article.time}
            </span>
          </div>
          <h3 className={`font-semibold mb-2 group-hover:text-blue-600 transition-colors ${
            darkMode ? 'text-white' : 'text-gray-800'
          }`}>
            {article.title}
          </h3>
          <p className={`text-sm mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            {article.summary}
          </p>
          <div className="flex items-center justify-between">
            <span className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              {article.source}
            </span>
            <ExternalLink size={14} className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} group-hover:text-blue-600 transition-colors`} />
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className={`${
        darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'
      } backdrop-blur-sm rounded-2xl p-6 border`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center text-2xl animate-pulse">
              {isHisAccount ? '👨‍💼' : '👩‍💄'}
            </div>
            <div>
              <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                Good {currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening'}, {user?.name}! 
                {isHisAccount ? ' 💪' : ' ✨'}
              </h1>
              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {currentTime.toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
              {currentTime.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
              })}
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <MapPin size={14} className="text-pink-500" />
              <span className={darkMode ? 'text-gray-300' : 'text-gray-600'}>Your Love Sanctuary</span>
            </div>
          </div>
        </div>

        {/* Love Quote */}
        <div className={`${
          darkMode ? 'bg-gradient-to-r from-purple-900/50 to-pink-900/50' : 'bg-gradient-to-r from-pink-50 to-purple-50'
        } rounded-xl p-4 text-center`}>
          <div className="flex items-center justify-center space-x-2 mb-2">
            <Heart className="text-pink-500 animate-pulse" size={20} />
            <span className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Daily Love Quote
            </span>
            <Heart className="text-pink-500 animate-pulse" size={20} />
          </div>
          <p className={`text-lg font-semibold italic ${darkMode ? 'text-white' : 'text-gray-800'}`}>
            "{quotes[currentQuote]}"
          </p>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className={`${
          darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'
        } backdrop-blur-sm rounded-xl p-4 border text-center`}>
          <div className="text-2xl mb-2">💕</div>
          <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>365+</div>
          <div className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Days Together</div>
        </div>
        <div className={`${
          darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'
        } backdrop-blur-sm rounded-xl p-4 border text-center`}>
          <div className="text-2xl mb-2">💬</div>
          <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>1,247</div>
          <div className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Sweet Messages</div>
        </div>
        <div className={`${
          darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'
        } backdrop-blur-sm rounded-xl p-4 border text-center`}>
          <div className="text-2xl mb-2">📸</div>
          <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>89</div>
          <div className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Beautiful Memories</div>
        </div>
      </div>

      {/* Personalized News Feed */}
      <div className={`${
        darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'
      } backdrop-blur-sm rounded-2xl p-6 border`}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="text-2xl">{isHisAccount ? '⚽' : '💄'}</div>
            <div>
              <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
                {isHisAccount ? 'Sports Updates' : 'Fashion & Beauty News'}
              </h2>
              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {isHisAccount ? 'Latest from the sports world' : 'Trending in fashion and beauty'}
              </p>
            </div>
          </div>
          <button className={`p-2 rounded-lg ${
            darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
          } transition-colors`}>
            <RefreshCw size={18} className={`${newsLoading ? 'animate-spin' : ''} ${
              darkMode ? 'text-gray-400' : 'text-gray-600'
            }`} />
          </button>
        </div>

        {newsLoading ? (
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className={`${
                darkMode ? 'bg-gray-700/50' : 'bg-gray-100'
              } rounded-xl p-4 animate-pulse`}>
                <div className="flex space-x-3">
                  <div className={`w-12 h-12 ${
                    darkMode ? 'bg-gray-600' : 'bg-gray-200'
                  } rounded-lg`}></div>
                  <div className="flex-1 space-y-2">
                    <div className={`h-4 ${
                      darkMode ? 'bg-gray-600' : 'bg-gray-200'
                    } rounded w-3/4`}></div>
                    <div className={`h-3 ${
                      darkMode ? 'bg-gray-600' : 'bg-gray-200'
                    } rounded w-1/2`}></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {(isHisAccount ? sportsNews : fashionNews).map(article => (
              <NewsCard key={article.id} article={article} type={isHisAccount ? 'sports' : 'fashion'} />
            ))}
          </div>
        )}
      </div>

      {/* Today's Agenda */}
      <div className={`${
        darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'
      } backdrop-blur-sm rounded-2xl p-6 border`}>
        <div className="flex items-center space-x-3 mb-4">
          <Calendar className="text-purple-500" size={24} />
          <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>
            Today's Love Agenda
          </h2>
        </div>
        <div className="space-y-3">
          <div className="flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-pink-500/10 to-purple-500/10">
            <Coffee className="text-pink-500" size={18} />
            <span className={darkMode ? 'text-gray-300' : 'text-gray-700'}>
              Morning coffee together ☕
            </span>
          </div>
          <div className="flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-blue-500/10 to-indigo-500/10">
            <Heart className="text-blue-500" size={18} />
            <span className={darkMode ? 'text-gray-300' : 'text-gray-700'}>
              Share a sweet message 💌
            </span>
          </div>
          <div className="flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10">
            <Star className="text-purple-500" size={18} />
            <span className={darkMode ? 'text-gray-300' : 'text-gray-700'}>
              Plan weekend date night 🌟
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
