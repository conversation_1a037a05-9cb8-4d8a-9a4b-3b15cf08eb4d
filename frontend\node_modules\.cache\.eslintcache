[{"C:\\Users\\<USER>\\me and her project\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Login.js": "4", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\context\\AuthContext.js": "5", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\services\\api.js": "6", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Dashboard.js": "7", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Chat.js": "8", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Photos.js": "9", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Notes.js": "10", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\hooks\\useKeyboardShortcuts.js": "11", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\LoadingTransition.js": "12", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\SettingsPanel.js": "13", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\context\\ThemeContext.js": "14", "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Home.js": "15"}, {"size": 535, "mtime": 1752911099796, "results": "16", "hashOfConfig": "17"}, {"size": 1669, "mtime": 1753689721096, "results": "18", "hashOfConfig": "17"}, {"size": 362, "mtime": 1752911100450, "results": "19", "hashOfConfig": "17"}, {"size": 15233, "mtime": 1753691708506, "results": "20", "hashOfConfig": "17"}, {"size": 1695, "mtime": 1752911881857, "results": "21", "hashOfConfig": "17"}, {"size": 1184, "mtime": 1752915277536, "results": "22", "hashOfConfig": "17"}, {"size": 10545, "mtime": 1753691332964, "results": "23", "hashOfConfig": "17"}, {"size": 8723, "mtime": 1753691633445, "results": "24", "hashOfConfig": "17"}, {"size": 23806, "mtime": 1753691485528, "results": "25", "hashOfConfig": "17"}, {"size": 14665, "mtime": 1753691779359, "results": "26", "hashOfConfig": "17"}, {"size": 609, "mtime": 1753690361136, "results": "27", "hashOfConfig": "17"}, {"size": 2867, "mtime": 1753690812117, "results": "28", "hashOfConfig": "17"}, {"size": 17126, "mtime": 1753690803844, "results": "29", "hashOfConfig": "17"}, {"size": 1165, "mtime": 1753690400453, "results": "30", "hashOfConfig": "17"}, {"size": 12202, "mtime": 1753691681381, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "we10pu", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\me and her project\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Login.js", ["77", "78"], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Chat.js", ["79", "80", "81"], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Photos.js", ["82", "83", "84", "85"], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Notes.js", ["86"], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\hooks\\useKeyboardShortcuts.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\LoadingTransition.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\SettingsPanel.js", ["87", "88", "89", "90"], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\context\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\me and her project\\frontend\\src\\components\\Home.js", ["91", "92", "93", "94"], [], {"ruleId": "95", "severity": 1, "message": "96", "line": 17, "column": 25, "nodeType": "97", "messageId": "98", "endLine": 17, "endColumn": 41}, {"ruleId": "95", "severity": 1, "message": "99", "line": 37, "column": 9, "nodeType": "97", "messageId": "98", "endLine": 37, "endColumn": 24}, {"ruleId": "95", "severity": 1, "message": "100", "line": 29, "column": 19, "nodeType": "97", "messageId": "98", "endLine": 29, "endColumn": 29}, {"ruleId": "95", "severity": 1, "message": "101", "line": 30, "column": 27, "nodeType": "97", "messageId": "98", "endLine": 30, "endColumn": 45}, {"ruleId": "95", "severity": 1, "message": "102", "line": 34, "column": 9, "nodeType": "97", "messageId": "98", "endLine": 34, "endColumn": 13}, {"ruleId": "95", "severity": 1, "message": "103", "line": 4, "column": 10, "nodeType": "97", "messageId": "98", "endLine": 4, "endColumn": 14}, {"ruleId": "95", "severity": 1, "message": "104", "line": 4, "column": 16, "nodeType": "97", "messageId": "98", "endLine": 4, "endColumn": 22}, {"ruleId": "95", "severity": 1, "message": "105", "line": 4, "column": 24, "nodeType": "97", "messageId": "98", "endLine": 4, "endColumn": 28}, {"ruleId": "95", "severity": 1, "message": "106", "line": 4, "column": 30, "nodeType": "97", "messageId": "98", "endLine": 4, "endColumn": 34}, {"ruleId": null, "fatal": true, "severity": 2, "message": "107", "line": 101, "column": 8, "nodeType": null}, {"ruleId": "95", "severity": 1, "message": "108", "line": 6, "column": 11, "nodeType": "97", "messageId": "98", "endLine": 6, "endColumn": 19}, {"ruleId": "95", "severity": 1, "message": "109", "line": 6, "column": 21, "nodeType": "97", "messageId": "98", "endLine": 6, "endColumn": 27}, {"ruleId": "95", "severity": 1, "message": "110", "line": 14, "column": 10, "nodeType": "97", "messageId": "98", "endLine": 14, "endColumn": 18}, {"ruleId": "95", "severity": 1, "message": "111", "line": 14, "column": 20, "nodeType": "97", "messageId": "98", "endLine": 14, "endColumn": 31}, {"ruleId": "95", "severity": 1, "message": "112", "line": 4, "column": 27, "nodeType": "97", "messageId": "98", "endLine": 4, "endColumn": 40}, {"ruleId": "95", "severity": 1, "message": "113", "line": 4, "column": 42, "nodeType": "97", "messageId": "98", "endLine": 4, "endColumn": 48}, {"ruleId": "95", "severity": 1, "message": "114", "line": 4, "column": 50, "nodeType": "97", "messageId": "98", "endLine": 4, "endColumn": 57}, {"ruleId": "95", "severity": 1, "message": "115", "line": 11, "column": 23, "nodeType": "97", "messageId": "98", "endLine": 11, "endColumn": 37}, "no-unused-vars", "'setMousePosition' is assigned a value but never used.", "Identifier", "unusedVar", "'FloatingElement' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'setOtherUserTyping' is assigned a value but never used.", "'user' is assigned a value but never used.", "'Plus' is defined but never used.", "'Search' is defined but never used.", "'Grid' is defined but never used.", "'List' is defined but never used.", "Parsing error: Identifier 'handleKeyPress' has already been declared. (101:8)", "'Download' is defined but never used.", "'Trash2' is defined but never used.", "'autoSave' is assigned a value but never used.", "'setAutoSave' is assigned a value but never used.", "'MessageSquare' is defined but never used.", "'Camera' is defined but never used.", "'PenTool' is defined but never used.", "'setNewsLoading' is assigned a value but never used."]